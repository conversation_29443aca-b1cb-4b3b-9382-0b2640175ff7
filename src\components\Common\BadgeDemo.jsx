import React from 'react';
import Badge, { StatusBadge } from './Badge';

/**
 * Badge Demo Component - Showcases all badge variants and features
 * This component demonstrates the modern badge system with various states and animations
 */
const BadgeDemo = () => {
  const statusLabels = {
    Active: 'Active',
    Inactive: 'Inactive',
    Pending: 'Pending',
    Approved: 'Approved',
    Rejected: 'Rejected',
    Submitted: 'Submitted',
    Expired: 'Expired'
  };

  return (
    <div className="badge-demo-container p-4">
      <div className="row">
        <div className="col-12">
          <h4 className="mb-4">Modern Badge System Demo</h4>
          
          {/* Basic Badge Variants */}
          <div className="mb-4">
            <h6 className="mb-3">Basic Badge Variants</h6>
            <div className="d-flex flex-wrap gap-3 mb-3">
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
              <Badge variant="danger">Danger</Badge>
              <Badge variant="info">Info</Badge>
              <Badge variant="secondary">Secondary</Badge>
            </div>
          </div>

          {/* Size Variants */}
          <div className="mb-4">
            <h6 className="mb-3">Size Variants</h6>
            <div className="d-flex flex-wrap align-items-center gap-3 mb-3">
              <Badge variant="success" size="small">Small</Badge>
              <Badge variant="info" size="medium">Medium</Badge>
              <Badge variant="warning" size="large">Large</Badge>
            </div>
          </div>

          {/* Status Badges */}
          <div className="mb-4">
            <h6 className="mb-3">Status Badges</h6>
            <div className="d-flex flex-wrap gap-3 mb-3">
              <StatusBadge status="Active" type="status" statusLabels={statusLabels} />
              <StatusBadge status="Inactive" type="status" statusLabels={statusLabels} />
            </div>
          </div>

          {/* KYC Status Badges */}
          <div className="mb-4">
            <h6 className="mb-3">KYC Status Badges</h6>
            <div className="d-flex flex-wrap gap-3 mb-3">
              <StatusBadge status="Approved" type="kyc" statusLabels={statusLabels} />
              <StatusBadge status="Rejected" type="kyc" statusLabels={statusLabels} />
              <StatusBadge status="Submitted" type="kyc" statusLabels={statusLabels} />
              <StatusBadge status="Pending" type="kyc" statusLabels={statusLabels} />
            </div>
          </div>

          {/* Subscription Status Badges */}
          <div className="mb-4">
            <h6 className="mb-3">Subscription Status Badges</h6>
            <div className="d-flex flex-wrap gap-3 mb-3">
              <StatusBadge status="Active" type="subscription" statusLabels={statusLabels} />
              <StatusBadge status="Expired" type="subscription" statusLabels={statusLabels} />
              <StatusBadge status="Pending" type="subscription" statusLabels={statusLabels} />
            </div>
          </div>

          {/* Animated vs Non-animated */}
          <div className="mb-4">
            <h6 className="mb-3">Animation Comparison</h6>
            <div className="d-flex flex-wrap gap-3 mb-3">
              <Badge variant="success" animated={true}>Animated</Badge>
              <Badge variant="success" animated={false}>Static</Badge>
            </div>
            <small className="text-muted">Hover over the badges to see the animation difference</small>
          </div>

          {/* Table Context Example */}
          <div className="mb-4">
            <h6 className="mb-3">Table Context Example</h6>
            <div className="table-responsive">
              <table className="table table-sm">
                <thead>
                  <tr>
                    <th>User</th>
                    <th>Status</th>
                    <th>KYC</th>
                    <th>Subscription</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>John Doe</td>
                    <td><StatusBadge status="Active" type="status" statusLabels={statusLabels} /></td>
                    <td><StatusBadge status="Approved" type="kyc" statusLabels={statusLabels} /></td>
                    <td><StatusBadge status="Active" type="subscription" statusLabels={statusLabels} /></td>
                  </tr>
                  <tr>
                    <td>Jane Smith</td>
                    <td><StatusBadge status="Inactive" type="status" statusLabels={statusLabels} /></td>
                    <td><StatusBadge status="Pending" type="kyc" statusLabels={statusLabels} /></td>
                    <td><StatusBadge status="Expired" type="subscription" statusLabels={statusLabels} /></td>
                  </tr>
                  <tr>
                    <td>Bob Johnson</td>
                    <td><StatusBadge status="Active" type="status" statusLabels={statusLabels} /></td>
                    <td><StatusBadge status="Rejected" type="kyc" statusLabels={statusLabels} /></td>
                    <td><StatusBadge status="Pending" type="subscription" statusLabels={statusLabels} /></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Usage Instructions */}
          <div className="mb-4">
            <h6 className="mb-3">Usage Instructions</h6>
            <div className="bg-light p-3 rounded">
              <h6>Basic Badge:</h6>
              <code>{`<Badge variant="success">Active</Badge>`}</code>
              
              <h6 className="mt-3">Status Badge:</h6>
              <code>{`<StatusBadge status="Active" type="status" statusLabels={statusLabels} />`}</code>
              
              <h6 className="mt-3">Available Variants:</h6>
              <ul className="mb-0">
                <li><code>success</code> - Green theme for active/approved states</li>
                <li><code>warning</code> - Orange theme for inactive/pending states</li>
                <li><code>danger</code> - Red theme for rejected/expired states</li>
                <li><code>info</code> - Blue theme for informational states</li>
                <li><code>secondary</code> - Gray theme for unknown/default states</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BadgeDemo;
