import React, { memo } from 'react';
import PropTypes from 'prop-types';

/**
 * Modern Badge Component with IDE-style design
 * 
 * Features:
 * - Professional styling with gradient backgrounds
 * - Hover animations and effects
 * - Dark/Light theme support
 * - Multiple variants (success, warning, danger, info, secondary)
 * - Responsive design
 * - Accessibility support
 */
const Badge = memo(({ 
  children, 
  variant = 'secondary', 
  size = 'medium',
  className = '',
  animated = true,
  ...props 
}) => {
  const baseClass = 'table-badge';
  const variantClass = `table-badge--${variant}`;
  const sizeClass = size !== 'medium' ? `table-badge--${size}` : '';
  const animatedClass = animated ? 'table-badge--animated' : '';
  
  const combinedClassName = [
    baseClass,
    variantClass,
    sizeClass,
    animatedClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <span 
      className={combinedClassName} 
      data-variant={variant}
      role="status"
      aria-label={`Status: ${children}`}
      {...props}
    >
      <span className="table-badge__content">
        <span className="table-badge__text">
          {children}
        </span>
      </span>
    </span>
  );
});

Badge.displayName = 'Badge';

Badge.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['success', 'warning', 'danger', 'info', 'secondary']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  className: PropTypes.string,
  animated: PropTypes.bool,
};

// Status Badge Component - specialized for status display
export const StatusBadge = memo(({ status, type, statusLabels, ...props }) => {
  const getVariantFromStatus = (status, type) => {
    if (type === 'status') {
      if (status === 'Active') return 'success';
      if (status === 'Inactive') return 'warning';
      return 'secondary';
    }
    
    if (type === 'kyc') {
      if (status === 'Approved') return 'success';
      if (status === 'Rejected') return 'danger';
      if (status === 'Submitted') return 'info';
      if (status === 'Pending') return 'warning';
      return 'secondary';
    }
    
    if (type === 'subscription') {
      if (status === 'Active') return 'success';
      if (status === 'Expired') return 'danger';
      if (status === 'Pending') return 'warning';
      return 'secondary';
    }
    
    return 'secondary';
  };

  const variant = getVariantFromStatus(status, type);
  const label = statusLabels?.[status] || status || 'Unknown';

  return (
    <Badge variant={variant} {...props}>
      {label}
    </Badge>
  );
});

StatusBadge.displayName = 'StatusBadge';

StatusBadge.propTypes = {
  status: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['status', 'kyc', 'subscription']).isRequired,
  statusLabels: PropTypes.object,
};

export default Badge;
